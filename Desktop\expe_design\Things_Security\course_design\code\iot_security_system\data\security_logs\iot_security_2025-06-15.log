2025-06-15 03:02:40 | INFO | __main__:main:464 | 开始系统测试...
2025-06-15 03:02:40 | INFO | __main__:create_directories:38 | 创建目录: data
2025-06-15 03:02:40 | INFO | __main__:create_directories:38 | 创建目录: data/security_logs
2025-06-15 03:02:40 | INFO | __main__:create_directories:38 | 创建目录: certs
2025-06-15 03:02:40 | INFO | __main__:create_directories:38 | 创建目录: keys
2025-06-15 03:02:40 | INFO | __main__:create_directories:38 | 创建目录: ca
2025-06-15 03:02:40 | INFO | __main__:test_modules:42 | 开始模块测试...
2025-06-15 03:02:40 | INFO | __main__:test_modules:46 | 测试设备管理器...
2025-06-15 03:02:40 | INFO | core.device_manager:_init_ca:85 | 证书颁发机构初始化成功
2025-06-15 03:02:40 | INFO | core.device_manager:load_device_registry:340 | 设备注册表文件不存在，创建新的注册表
2025-06-15 03:02:40 | INFO | core.device_manager:register_device:152 | 设备注册成功: test_001, 安全等级: 1
2025-06-15 03:02:40 | INFO | __main__:test_modules:61 | ✓ 设备管理器测试通过
2025-06-15 03:02:40 | INFO | __main__:test_modules:66 | 测试加密引擎...
2025-06-15 03:02:40 | INFO | core.crypto_engine:generate_key:246 | 生成SM4密钥，长度: 16字节
2025-06-15 03:02:40 | INFO | core.crypto_engine:generate_key:246 | 生成LED密钥，长度: 8字节
2025-06-15 03:02:40 | INFO | __main__:test_modules:83 | ✓ SM4加密引擎测试通过
2025-06-15 03:02:40 | INFO | __main__:test_modules:96 | ✓ LED加密引擎测试通过
2025-06-15 03:02:40 | INFO | __main__:test_modules:103 | 测试威胁检测器...
2025-06-15 03:02:40 | INFO | core.threat_detector:_load_threat_intelligence:367 | 威胁情报文件不存在，使用默认配置
2025-06-15 03:02:40 | INFO | __main__:test_modules:125 | ✓ 威胁检测器测试通过，检测到0个威胁
2025-06-15 03:02:40 | INFO | __main__:test_modules:127 | 所有模块测试完成
2025-06-15 03:02:40 | INFO | __main__:main:467 | 系统测试通过
2025-06-15 03:03:00 | INFO | __main__:main:477 | 启动物联网安全防护系统...
2025-06-15 03:03:00 | INFO | __main__:create_directories:38 | 创建目录: data
2025-06-15 03:03:00 | INFO | __main__:create_directories:38 | 创建目录: data/security_logs
2025-06-15 03:03:00 | INFO | __main__:create_directories:38 | 创建目录: certs
2025-06-15 03:03:00 | INFO | __main__:create_directories:38 | 创建目录: keys
2025-06-15 03:03:00 | INFO | __main__:create_directories:38 | 创建目录: ca
2025-06-15 03:03:01 | INFO | core.device_manager:_init_ca:85 | 证书颁发机构初始化成功
2025-06-15 03:03:01 | INFO | core.device_manager:load_device_registry:337 | 加载设备注册表成功，共1个设备
2025-06-15 03:03:01 | INFO | core.threat_detector:_load_threat_intelligence:367 | 威胁情报文件不存在，使用默认配置
2025-06-15 03:03:01 | INFO | __main__:start:177 | ============================================================
2025-06-15 03:03:01 | INFO | __main__:start:178 | 物联网安全防护系统启动中...
2025-06-15 03:03:01 | INFO | __main__:start:179 | 系统版本: IoT Security Protection System v1.0.0
2025-06-15 03:03:01 | INFO | __main__:start:180 | ============================================================
2025-06-15 03:03:01 | INFO | __main__:_initialize_modules:208 | 初始化设备管理模块...
2025-06-15 03:03:01 | INFO | __main__:_initialize_modules:211 | 初始化加密引擎...
2025-06-15 03:03:01 | INFO | core.crypto_engine:generate_key:246 | 生成SM4密钥，长度: 16字节
2025-06-15 03:03:01 | INFO | __main__:_initialize_modules:214 | 生成测试密钥成功，长度: 16字节
2025-06-15 03:03:01 | INFO | __main__:_initialize_modules:216 | 初始化威胁检测模块...
2025-06-15 03:03:01 | INFO | core.threat_detector:_load_threat_intelligence:367 | 威胁情报文件不存在，使用默认配置
2025-06-15 03:03:01 | INFO | __main__:_initialize_modules:220 | 所有模块初始化完成
2025-06-15 03:03:01 | INFO | __main__:start:196 | 系统启动完成，开始监控...
2025-06-15 03:03:01 | WARNING | utils.logger:log_security_event:67 | [SYSTEM_START] 物联网安全防护系统启动成功
2025-06-15 03:03:01 | INFO | __main__:_system_monitor:275 | 系统状态 - 运行时间: 0:00:00.003647, 设备数量: 1, 威胁检测: 0
2025-06-15 03:03:01 | INFO | __main__:_demo_simulation:287 | 开始演示模拟...
2025-06-15 03:03:01 | INFO | core.device_manager:register_device:152 | 设备注册成功: sensor_001, 安全等级: 4
2025-06-15 03:03:01 | INFO | __main__:_simulate_device_registration:334 | 演示设备注册成功: sensor_001
2025-06-15 03:03:03 | INFO | core.device_manager:register_device:152 | 设备注册成功: camera_001, 安全等级: 5
2025-06-15 03:03:03 | INFO | __main__:_simulate_device_registration:334 | 演示设备注册成功: camera_001
2025-06-15 03:03:05 | INFO | core.device_manager:register_device:152 | 设备注册成功: gateway_001, 安全等级: 4
2025-06-15 03:03:05 | INFO | __main__:_simulate_device_registration:334 | 演示设备注册成功: gateway_001
2025-06-15 03:04:07 | INFO | __main__:main:466 | 开始系统测试...
2025-06-15 03:04:07 | INFO | __main__:create_directories:38 | 创建目录: data
2025-06-15 03:04:07 | INFO | __main__:create_directories:38 | 创建目录: data/security_logs
2025-06-15 03:04:07 | INFO | __main__:create_directories:38 | 创建目录: certs
2025-06-15 03:04:07 | INFO | __main__:create_directories:38 | 创建目录: keys
2025-06-15 03:04:07 | INFO | __main__:create_directories:38 | 创建目录: ca
2025-06-15 03:04:07 | INFO | __main__:test_modules:42 | 开始模块测试...
2025-06-15 03:04:07 | INFO | __main__:test_modules:46 | 测试设备管理器...
2025-06-15 03:04:07 | INFO | core.device_manager:_init_ca:85 | 证书颁发机构初始化成功
2025-06-15 03:04:07 | INFO | core.device_manager:load_device_registry:337 | 加载设备注册表成功，共4个设备
2025-06-15 03:04:07 | ERROR | __main__:test_modules:63 | ✗ 设备管理器测试失败: 设备已存在
2025-06-15 03:04:07 | INFO | __main__:test_modules:66 | 测试加密引擎...
2025-06-15 03:04:07 | INFO | core.crypto_engine:generate_key:246 | 生成SM4密钥，长度: 16字节
2025-06-15 03:04:07 | INFO | core.crypto_engine:generate_key:246 | 生成LED密钥，长度: 8字节
2025-06-15 03:04:07 | INFO | __main__:test_modules:83 | ✓ SM4加密引擎测试通过
2025-06-15 03:04:07 | INFO | __main__:test_modules:96 | ✓ LED加密引擎测试通过
2025-06-15 03:04:07 | INFO | __main__:test_modules:103 | 测试威胁检测器...
2025-06-15 03:04:07 | INFO | __main__:test_modules:125 | ✓ 威胁检测器测试通过，检测到0个威胁
2025-06-15 03:04:07 | INFO | __main__:test_modules:127 | 所有模块测试完成
2025-06-15 03:04:07 | INFO | __main__:main:469 | 系统测试通过
2025-06-15 03:04:24 | INFO | __main__:main:466 | 开始系统测试...
2025-06-15 03:04:24 | INFO | __main__:create_directories:38 | 创建目录: data
2025-06-15 03:04:24 | INFO | __main__:create_directories:38 | 创建目录: data/security_logs
2025-06-15 03:04:24 | INFO | __main__:create_directories:38 | 创建目录: certs
2025-06-15 03:04:24 | INFO | __main__:create_directories:38 | 创建目录: keys
2025-06-15 03:04:24 | INFO | __main__:create_directories:38 | 创建目录: ca
2025-06-15 03:04:24 | INFO | __main__:test_modules:42 | 开始模块测试...
2025-06-15 03:04:24 | INFO | __main__:test_modules:46 | 测试设备管理器...
2025-06-15 03:04:24 | INFO | core.device_manager:_init_ca:85 | 证书颁发机构初始化成功
2025-06-15 03:04:24 | INFO | core.device_manager:load_device_registry:337 | 加载设备注册表成功，共4个设备
2025-06-15 03:04:24 | ERROR | __main__:test_modules:63 | ✗ 设备管理器测试失败: 设备已存在
2025-06-15 03:04:24 | INFO | __main__:test_modules:66 | 测试加密引擎...
2025-06-15 03:04:24 | INFO | core.crypto_engine:generate_key:246 | 生成SM4密钥，长度: 16字节
2025-06-15 03:04:24 | INFO | core.crypto_engine:generate_key:246 | 生成LED密钥，长度: 8字节
2025-06-15 03:04:24 | INFO | __main__:test_modules:83 | ✓ SM4加密引擎测试通过
2025-06-15 03:04:24 | INFO | __main__:test_modules:96 | ✓ LED加密引擎测试通过
2025-06-15 03:04:24 | INFO | __main__:test_modules:103 | 测试威胁检测器...
2025-06-15 03:04:24 | INFO | __main__:test_modules:125 | ✓ 威胁检测器测试通过，检测到0个威胁
2025-06-15 03:04:24 | INFO | __main__:test_modules:127 | 所有模块测试完成
2025-06-15 03:04:24 | INFO | __main__:main:469 | 系统测试通过
2025-06-15 03:04:58 | INFO | __main__:main:470 | 开始系统测试...
2025-06-15 03:04:58 | INFO | __main__:create_directories:38 | 创建目录: data
2025-06-15 03:04:58 | INFO | __main__:create_directories:38 | 创建目录: data/security_logs
2025-06-15 03:04:58 | INFO | __main__:create_directories:38 | 创建目录: certs
2025-06-15 03:04:58 | INFO | __main__:create_directories:38 | 创建目录: keys
2025-06-15 03:04:58 | INFO | __main__:create_directories:38 | 创建目录: ca
2025-06-15 03:04:58 | INFO | __main__:test_modules:42 | 开始模块测试...
2025-06-15 03:04:58 | INFO | __main__:test_modules:46 | 测试设备管理器...
2025-06-15 03:04:58 | INFO | core.device_manager:_init_ca:85 | 证书颁发机构初始化成功
2025-06-15 03:04:58 | INFO | core.device_manager:load_device_registry:337 | 加载设备注册表成功，共4个设备
2025-06-15 03:04:58 | INFO | core.device_manager:register_device:152 | 设备注册成功: test_001, 安全等级: 1
2025-06-15 03:04:58 | INFO | __main__:test_modules:65 | ✓ 设备管理器测试通过
2025-06-15 03:04:58 | INFO | __main__:test_modules:70 | 测试加密引擎...
2025-06-15 03:04:58 | INFO | core.crypto_engine:generate_key:246 | 生成SM4密钥，长度: 16字节
2025-06-15 03:04:58 | INFO | core.crypto_engine:generate_key:246 | 生成LED密钥，长度: 8字节
2025-06-15 03:04:58 | INFO | __main__:test_modules:87 | ✓ SM4加密引擎测试通过
2025-06-15 03:04:58 | INFO | __main__:test_modules:100 | ✓ LED加密引擎测试通过
2025-06-15 03:04:58 | INFO | __main__:test_modules:107 | 测试威胁检测器...
2025-06-15 03:04:58 | INFO | __main__:test_modules:129 | ✓ 威胁检测器测试通过，检测到0个威胁
2025-06-15 03:04:58 | INFO | __main__:test_modules:131 | 所有模块测试完成
2025-06-15 03:04:58 | INFO | __main__:main:473 | 系统测试通过
