"""
日志工具模块
提供统一的日志记录功能
"""

import os
import sys
from loguru import logger
from config.settings import SecurityConfig

def setup_logger():
    """设置日志配置"""
    # 移除默认处理器
    logger.remove()
    
    # 创建日志目录
    log_dir = SecurityConfig.LOGGING['log_dir']
    os.makedirs(log_dir, exist_ok=True)
    
    # 控制台输出
    logger.add(
        sys.stdout,
        format=SecurityConfig.LOGGING['format'],
        level=SecurityConfig.LOGGING['level'],
        colorize=True
    )
    
    # 文件输出 - 普通日志
    logger.add(
        os.path.join(log_dir, "iot_security_{time:YYYY-MM-DD}.log"),
        format=SecurityConfig.LOGGING['format'],
        level="INFO",
        rotation=SecurityConfig.LOGGING['rotation'],
        retention=SecurityConfig.LOGGING['retention'],
        encoding="utf-8"
    )
    
    # 文件输出 - 错误日志
    logger.add(
        os.path.join(log_dir, "error_{time:YYYY-MM-DD}.log"),
        format=SecurityConfig.LOGGING['format'],
        level="ERROR",
        rotation=SecurityConfig.LOGGING['rotation'],
        retention=SecurityConfig.LOGGING['retention'],
        encoding="utf-8"
    )
    
    # 文件输出 - 安全事件日志
    logger.add(
        os.path.join(log_dir, "security_events_{time:YYYY-MM-DD}.log"),
        format=SecurityConfig.LOGGING['format'],
        level="WARNING",
        rotation=SecurityConfig.LOGGING['rotation'],
        retention=SecurityConfig.LOGGING['retention'],
        encoding="utf-8",
        filter=lambda record: "security" in record["extra"]
    )

def get_logger(name: str = None):
    """获取日志记录器"""
    if name:
        return logger.bind(name=name)
    return logger

def log_security_event(event_type: str, message: str, **kwargs):
    """记录安全事件"""
    logger.bind(security=True).warning(f"[{event_type}] {message}", **kwargs)

def log_device_event(device_id: str, event: str, **kwargs):
    """记录设备事件"""
    logger.bind(device=device_id).info(f"[DEVICE] {event}", **kwargs)

def log_threat_event(threat_type: str, severity: str, message: str, **kwargs):
    """记录威胁事件"""
    logger.bind(security=True, threat=threat_type).warning(
        f"[THREAT-{severity.upper()}] {message}", **kwargs
    )

# 初始化日志配置
setup_logger()
