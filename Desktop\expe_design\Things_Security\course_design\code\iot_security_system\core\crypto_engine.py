"""
加密引擎模块
实现论文中提到的轻量级加密算法和国密算法
"""

import os
import hashlib
import hmac
from typing import Tuple, Optional, Dict, Any
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend
import struct

from config.settings import CryptoConfig
from utils.logger import get_logger

logger = get_logger(__name__)

class LightweightCrypto:
    """轻量级加密算法实现"""
    
    @staticmethod
    def led_encrypt(plaintext: bytes, key: bytes) -> bytes:
        """
        LED-64轻量级加密算法实现
        基于论文中提到的轻量级加密需求
        """
        if len(key) != 8:  # 64位密钥
            raise ValueError("LED-64需要64位密钥")
        
        # 简化的LED算法实现（实际应用中需要完整实现）
        # 这里使用AES作为替代演示
        from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
        
        # 扩展密钥到128位用于AES
        extended_key = key * 2
        cipher = Cipher(algorithms.AES(extended_key), modes.ECB(), backend=default_backend())
        encryptor = cipher.encryptor()
        
        # 填充到16字节对齐
        padded_plaintext = plaintext + b'\x00' * (16 - len(plaintext) % 16)
        ciphertext = encryptor.update(padded_plaintext) + encryptor.finalize()
        
        logger.debug(f"LED加密完成，原文长度: {len(plaintext)}, 密文长度: {len(ciphertext)}")
        return ciphertext
    
    @staticmethod
    def led_decrypt(ciphertext: bytes, key: bytes) -> bytes:
        """LED-64解密"""
        if len(key) != 8:
            raise ValueError("LED-64需要64位密钥")
        
        extended_key = key * 2
        cipher = Cipher(algorithms.AES(extended_key), modes.ECB(), backend=default_backend())
        decryptor = cipher.decryptor()
        
        plaintext = decryptor.update(ciphertext) + decryptor.finalize()
        # 移除填充
        plaintext = plaintext.rstrip(b'\x00')
        
        logger.debug(f"LED解密完成，密文长度: {len(ciphertext)}, 原文长度: {len(plaintext)}")
        return plaintext
    
    @staticmethod
    def present_encrypt(plaintext: bytes, key: bytes) -> bytes:
        """
        PRESENT轻量级加密算法
        80位密钥，64位分组
        """
        if len(key) != 10:  # 80位密钥
            raise ValueError("PRESENT需要80位密钥")
        
        # 简化实现，实际中需要完整的PRESENT算法
        # 使用ChaCha20作为轻量级替代
        from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
        
        # 生成随机nonce
        nonce = os.urandom(12)
        # 扩展密钥到256位
        extended_key = (key * 4)[:32]
        
        cipher = Cipher(algorithms.ChaCha20(extended_key, nonce), mode=None, backend=default_backend())
        encryptor = cipher.encryptor()
        ciphertext = encryptor.update(plaintext) + encryptor.finalize()
        
        # 返回nonce + ciphertext
        return nonce + ciphertext

class SM2Crypto:
    """国密SM2算法实现"""
    
    def __init__(self):
        self.private_key = None
        self.public_key = None
    
    def generate_keypair(self) -> Tuple[bytes, bytes]:
        """生成SM2密钥对"""
        # 使用RSA模拟SM2（实际应用中应使用真正的SM2实现）
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
            backend=default_backend()
        )
        
        self.private_key = private_key
        self.public_key = private_key.public_key()
        
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        public_pem = self.public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        logger.info("SM2密钥对生成成功")
        return private_pem, public_pem
    
    def sign(self, message: bytes, private_key_pem: bytes) -> bytes:
        """SM2数字签名"""
        private_key = serialization.load_pem_private_key(
            private_key_pem, password=None, backend=default_backend()
        )
        
        signature = private_key.sign(
            message,
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        
        logger.debug(f"SM2签名完成，消息长度: {len(message)}")
        return signature
    
    def verify(self, message: bytes, signature: bytes, public_key_pem: bytes) -> bool:
        """SM2签名验证"""
        try:
            public_key = serialization.load_pem_public_key(
                public_key_pem, backend=default_backend()
            )
            
            public_key.verify(
                signature,
                message,
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            
            logger.debug("SM2签名验证成功")
            return True
        except Exception as e:
            logger.warning(f"SM2签名验证失败: {e}")
            return False

class SM3Hash:
    """国密SM3哈希算法"""
    
    @staticmethod
    def hash(data: bytes) -> bytes:
        """SM3哈希计算"""
        # 使用SHA256模拟SM3（实际应用中应使用真正的SM3实现）
        digest = hashes.Hash(hashes.SHA256(), backend=default_backend())
        digest.update(data)
        result = digest.finalize()
        
        logger.debug(f"SM3哈希计算完成，数据长度: {len(data)}")
        return result
    
    @staticmethod
    def hmac(key: bytes, data: bytes) -> bytes:
        """基于SM3的HMAC"""
        return hmac.new(key, data, hashlib.sha256).digest()

class SM4Cipher:
    """国密SM4对称加密算法"""
    
    @staticmethod
    def encrypt(plaintext: bytes, key: bytes) -> Tuple[bytes, bytes]:
        """SM4加密"""
        if len(key) != 16:  # 128位密钥
            raise ValueError("SM4需要128位密钥")
        
        # 使用AES模拟SM4（实际应用中应使用真正的SM4实现）
        iv = os.urandom(16)
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
        encryptor = cipher.encryptor()
        
        # PKCS7填充
        pad_len = 16 - (len(plaintext) % 16)
        padded_plaintext = plaintext + bytes([pad_len] * pad_len)
        
        ciphertext = encryptor.update(padded_plaintext) + encryptor.finalize()
        
        logger.debug(f"SM4加密完成，原文长度: {len(plaintext)}")
        return ciphertext, iv
    
    @staticmethod
    def decrypt(ciphertext: bytes, key: bytes, iv: bytes) -> bytes:
        """SM4解密"""
        if len(key) != 16:
            raise ValueError("SM4需要128位密钥")
        
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
        decryptor = cipher.decryptor()
        
        padded_plaintext = decryptor.update(ciphertext) + decryptor.finalize()
        
        # 移除PKCS7填充
        pad_len = padded_plaintext[-1]
        plaintext = padded_plaintext[:-pad_len]
        
        logger.debug(f"SM4解密完成，密文长度: {len(ciphertext)}")
        return plaintext

class CryptoEngine:
    """加密引擎主类"""
    
    def __init__(self):
        self.sm2 = SM2Crypto()
        self.lightweight = LightweightCrypto()
        self.key_cache: Dict[str, bytes] = {}
        
    def generate_key(self, algorithm: str, key_size: int = None) -> bytes:
        """生成密钥"""
        if algorithm.upper() == 'SM4':
            key = os.urandom(16)  # 128位
        elif algorithm.upper() == 'LED':
            key = os.urandom(8)   # 64位
        elif algorithm.upper() == 'PRESENT':
            key = os.urandom(10)  # 80位
        else:
            key_size = key_size or 32
            key = os.urandom(key_size)
        
        logger.info(f"生成{algorithm}密钥，长度: {len(key)}字节")
        return key
    
    def derive_key(self, password: str, salt: bytes = None, 
                   iterations: int = 100000) -> bytes:
        """密钥派生"""
        if salt is None:
            salt = os.urandom(16)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=iterations,
            backend=default_backend()
        )
        
        key = kdf.derive(password.encode('utf-8'))
        logger.info("密钥派生完成")
        return key
    
    def encrypt_data(self, data: bytes, algorithm: str, key: bytes) -> Dict[str, Any]:
        """数据加密"""
        try:
            if algorithm.upper() == 'SM4':
                ciphertext, iv = SM4Cipher.encrypt(data, key)
                return {
                    'algorithm': 'SM4',
                    'ciphertext': ciphertext,
                    'iv': iv,
                    'success': True
                }
            elif algorithm.upper() == 'LED':
                ciphertext = self.lightweight.led_encrypt(data, key)
                return {
                    'algorithm': 'LED',
                    'ciphertext': ciphertext,
                    'success': True
                }
            elif algorithm.upper() == 'PRESENT':
                ciphertext = self.lightweight.present_encrypt(data, key)
                return {
                    'algorithm': 'PRESENT',
                    'ciphertext': ciphertext,
                    'success': True
                }
            else:
                raise ValueError(f"不支持的加密算法: {algorithm}")
                
        except Exception as e:
            logger.error(f"数据加密失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def decrypt_data(self, ciphertext: bytes, algorithm: str, 
                    key: bytes, iv: bytes = None) -> Dict[str, Any]:
        """数据解密"""
        try:
            if algorithm.upper() == 'SM4':
                if iv is None:
                    raise ValueError("SM4解密需要IV")
                plaintext = SM4Cipher.decrypt(ciphertext, key, iv)
                return {
                    'algorithm': 'SM4',
                    'plaintext': plaintext,
                    'success': True
                }
            elif algorithm.upper() == 'LED':
                plaintext = self.lightweight.led_decrypt(ciphertext, key)
                return {
                    'algorithm': 'LED',
                    'plaintext': plaintext,
                    'success': True
                }
            else:
                raise ValueError(f"不支持的解密算法: {algorithm}")
                
        except Exception as e:
            logger.error(f"数据解密失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def hash_data(self, data: bytes, algorithm: str = 'SM3') -> bytes:
        """数据哈希"""
        if algorithm.upper() == 'SM3':
            return SM3Hash.hash(data)
        else:
            # 默认使用SHA256
            digest = hashes.Hash(hashes.SHA256(), backend=default_backend())
            digest.update(data)
            return digest.finalize()
    
    def sign_data(self, data: bytes, private_key_pem: bytes) -> bytes:
        """数据签名"""
        return self.sm2.sign(data, private_key_pem)
    
    def verify_signature(self, data: bytes, signature: bytes, 
                        public_key_pem: bytes) -> bool:
        """验证签名"""
        return self.sm2.verify(data, signature, public_key_pem)
    
    def get_algorithm_info(self) -> Dict[str, Dict]:
        """获取支持的算法信息"""
        return {
            'symmetric': {
                'SM4': {'key_size': 128, 'block_size': 128},
                'LED': {'key_size': 64, 'block_size': 64},
                'PRESENT': {'key_size': 80, 'block_size': 64}
            },
            'asymmetric': {
                'SM2': {'key_size': 256}
            },
            'hash': {
                'SM3': {'digest_size': 256},
                'SHA256': {'digest_size': 256}
            }
        }
