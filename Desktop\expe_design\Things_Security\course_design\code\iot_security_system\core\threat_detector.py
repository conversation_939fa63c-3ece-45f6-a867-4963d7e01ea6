"""
威胁检测模块
实现论文中提到的基于机器学习的入侵检测系统
"""

import time
import json
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime
from collections import defaultdict, deque
import joblib
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split

from config.settings import SecurityConfig
from utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class ThreatEvent:
    """威胁事件数据类"""
    event_id: str
    device_id: str
    event_type: str  # anomaly, intrusion, malware, ddos
    severity: str    # low, medium, high, critical
    description: str
    timestamp: float
    source_ip: str
    destination_ip: str
    protocol: str
    payload_size: int
    features: Dict[str, float]
    confidence: float
    status: str  # detected, investigating, resolved, false_positive

@dataclass
class NetworkFlow:
    """网络流量数据类"""
    src_ip: str
    dst_ip: str
    src_port: int
    dst_port: int
    protocol: str
    packet_count: int
    byte_count: int
    duration: float
    flags: List[str]
    timestamp: float

class AnomalyDetector:
    """异常检测器"""
    
    def __init__(self):
        self.model = IsolationForest(
            contamination=0.1,  # 假设10%的数据是异常
            random_state=42,
            n_estimators=100
        )
        self.scaler = StandardScaler()
        self.is_trained = False
        self.feature_names = [
            'packet_rate', 'byte_rate', 'avg_packet_size',
            'connection_count', 'unique_dst_count', 'protocol_diversity',
            'time_interval', 'port_scan_score', 'payload_entropy'
        ]
    
    def extract_features(self, flows: List[NetworkFlow], 
                        time_window: int = 60) -> np.ndarray:
        """从网络流量中提取特征"""
        if not flows:
            return np.zeros((1, len(self.feature_names)))
        
        current_time = time.time()
        recent_flows = [f for f in flows 
                       if current_time - f.timestamp <= time_window]
        
        if not recent_flows:
            return np.zeros((1, len(self.feature_names)))
        
        # 计算特征
        total_packets = sum(f.packet_count for f in recent_flows)
        total_bytes = sum(f.byte_count for f in recent_flows)
        total_duration = sum(f.duration for f in recent_flows)
        
        # 包速率和字节速率
        packet_rate = total_packets / time_window if time_window > 0 else 0
        byte_rate = total_bytes / time_window if time_window > 0 else 0
        
        # 平均包大小
        avg_packet_size = total_bytes / total_packets if total_packets > 0 else 0
        
        # 连接数量
        connection_count = len(recent_flows)
        
        # 唯一目标数量
        unique_dst_count = len(set(f.dst_ip for f in recent_flows))
        
        # 协议多样性
        protocols = set(f.protocol for f in recent_flows)
        protocol_diversity = len(protocols)
        
        # 时间间隔方差
        timestamps = [f.timestamp for f in recent_flows]
        time_intervals = np.diff(sorted(timestamps)) if len(timestamps) > 1 else [0]
        time_interval = np.var(time_intervals) if time_intervals else 0
        
        # 端口扫描评分
        port_scan_score = self._calculate_port_scan_score(recent_flows)
        
        # 载荷熵（简化计算）
        payload_entropy = self._calculate_payload_entropy(recent_flows)
        
        features = np.array([[
            packet_rate, byte_rate, avg_packet_size, connection_count,
            unique_dst_count, protocol_diversity, time_interval,
            port_scan_score, payload_entropy
        ]])
        
        return features
    
    def _calculate_port_scan_score(self, flows: List[NetworkFlow]) -> float:
        """计算端口扫描评分"""
        src_port_counts = defaultdict(set)
        for flow in flows:
            src_port_counts[flow.src_ip].add(flow.dst_port)
        
        max_ports = max(len(ports) for ports in src_port_counts.values()) if src_port_counts else 0
        return min(max_ports / 100.0, 1.0)  # 归一化到0-1
    
    def _calculate_payload_entropy(self, flows: List[NetworkFlow]) -> float:
        """计算载荷熵（简化版本）"""
        # 基于包大小分布计算熵
        sizes = [f.byte_count for f in flows if f.byte_count > 0]
        if not sizes:
            return 0.0
        
        # 计算大小分布的熵
        size_counts = defaultdict(int)
        for size in sizes:
            # 将大小分组到桶中
            bucket = size // 100
            size_counts[bucket] += 1
        
        total = len(sizes)
        entropy = 0.0
        for count in size_counts.values():
            p = count / total
            if p > 0:
                entropy -= p * np.log2(p)
        
        return entropy
    
    def train(self, normal_flows: List[List[NetworkFlow]]) -> bool:
        """训练异常检测模型"""
        try:
            # 提取特征
            features_list = []
            for flows in normal_flows:
                features = self.extract_features(flows)
                features_list.append(features[0])
            
            if not features_list:
                logger.error("没有足够的训练数据")
                return False
            
            X = np.array(features_list)
            
            # 标准化特征
            X_scaled = self.scaler.fit_transform(X)
            
            # 训练模型
            self.model.fit(X_scaled)
            self.is_trained = True
            
            logger.info(f"异常检测模型训练完成，训练样本数: {len(features_list)}")
            return True
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            return False
    
    def detect_anomaly(self, flows: List[NetworkFlow]) -> Tuple[bool, float]:
        """检测异常"""
        if not self.is_trained:
            logger.warning("模型未训练，无法进行异常检测")
            return False, 0.0
        
        try:
            features = self.extract_features(flows)
            features_scaled = self.scaler.transform(features)
            
            # 预测异常
            anomaly_score = self.model.decision_function(features_scaled)[0]
            is_anomaly = self.model.predict(features_scaled)[0] == -1
            
            # 将异常分数转换为置信度
            confidence = abs(anomaly_score)
            
            return is_anomaly, confidence
            
        except Exception as e:
            logger.error(f"异常检测失败: {e}")
            return False, 0.0

class IntrusionDetector:
    """入侵检测器"""
    
    def __init__(self):
        self.attack_patterns = self._load_attack_patterns()
        self.suspicious_ips = set()
        self.failed_attempts = defaultdict(int)
        self.rate_limiters = defaultdict(lambda: deque(maxlen=100))
    
    def _load_attack_patterns(self) -> Dict[str, Dict]:
        """加载攻击模式"""
        return {
            'port_scan': {
                'description': '端口扫描攻击',
                'indicators': ['high_port_diversity', 'rapid_connections'],
                'threshold': 50
            },
            'brute_force': {
                'description': '暴力破解攻击',
                'indicators': ['repeated_failed_auth', 'same_source_ip'],
                'threshold': 10
            },
            'ddos': {
                'description': 'DDoS攻击',
                'indicators': ['high_request_rate', 'multiple_sources'],
                'threshold': 1000
            },
            'malware_communication': {
                'description': '恶意软件通信',
                'indicators': ['suspicious_domains', 'encrypted_payload'],
                'threshold': 5
            }
        }
    
    def detect_port_scan(self, flows: List[NetworkFlow], 
                        time_window: int = 60) -> List[ThreatEvent]:
        """检测端口扫描攻击"""
        threats = []
        current_time = time.time()
        
        # 按源IP分组
        ip_port_map = defaultdict(set)
        for flow in flows:
            if current_time - flow.timestamp <= time_window:
                ip_port_map[flow.src_ip].add(flow.dst_port)
        
        # 检测端口扫描
        for src_ip, ports in ip_port_map.items():
            if len(ports) > self.attack_patterns['port_scan']['threshold']:
                threat = ThreatEvent(
                    event_id=f"port_scan_{src_ip}_{int(current_time)}",
                    device_id="network_monitor",
                    event_type="intrusion",
                    severity="medium",
                    description=f"检测到来自{src_ip}的端口扫描攻击，扫描了{len(ports)}个端口",
                    timestamp=current_time,
                    source_ip=src_ip,
                    destination_ip="multiple",
                    protocol="TCP",
                    payload_size=0,
                    features={'port_count': len(ports), 'scan_rate': len(ports)/time_window},
                    confidence=min(len(ports) / 100.0, 1.0),
                    status="detected"
                )
                threats.append(threat)
                self.suspicious_ips.add(src_ip)
        
        return threats
    
    def detect_ddos(self, flows: List[NetworkFlow], 
                   time_window: int = 60) -> List[ThreatEvent]:
        """检测DDoS攻击"""
        threats = []
        current_time = time.time()
        
        # 计算请求速率
        recent_flows = [f for f in flows 
                       if current_time - f.timestamp <= time_window]
        
        request_rate = len(recent_flows) / time_window if time_window > 0 else 0
        
        if request_rate > self.attack_patterns['ddos']['threshold']:
            # 分析攻击源
            source_ips = set(f.src_ip for f in recent_flows)
            
            threat = ThreatEvent(
                event_id=f"ddos_{int(current_time)}",
                device_id="network_monitor",
                event_type="ddos",
                severity="high",
                description=f"检测到DDoS攻击，请求速率: {request_rate:.2f}/秒，攻击源数量: {len(source_ips)}",
                timestamp=current_time,
                source_ip="multiple",
                destination_ip="network",
                protocol="multiple",
                payload_size=sum(f.byte_count for f in recent_flows),
                features={
                    'request_rate': request_rate,
                    'source_count': len(source_ips),
                    'total_bytes': sum(f.byte_count for f in recent_flows)
                },
                confidence=min(request_rate / 2000.0, 1.0),
                status="detected"
            )
            threats.append(threat)
        
        return threats
    
    def detect_brute_force(self, auth_attempts: List[Dict]) -> List[ThreatEvent]:
        """检测暴力破解攻击"""
        threats = []
        current_time = time.time()
        
        # 统计失败尝试
        for attempt in auth_attempts:
            if not attempt.get('success', True):
                src_ip = attempt.get('source_ip', 'unknown')
                self.failed_attempts[src_ip] += 1
        
        # 检测暴力破解
        for src_ip, failed_count in self.failed_attempts.items():
            if failed_count > self.attack_patterns['brute_force']['threshold']:
                threat = ThreatEvent(
                    event_id=f"brute_force_{src_ip}_{int(current_time)}",
                    device_id="auth_monitor",
                    event_type="intrusion",
                    severity="high",
                    description=f"检测到来自{src_ip}的暴力破解攻击，失败尝试次数: {failed_count}",
                    timestamp=current_time,
                    source_ip=src_ip,
                    destination_ip="auth_server",
                    protocol="HTTP",
                    payload_size=0,
                    features={'failed_attempts': failed_count},
                    confidence=min(failed_count / 50.0, 1.0),
                    status="detected"
                )
                threats.append(threat)
                self.suspicious_ips.add(src_ip)
        
        return threats

class ThreatDetector:
    """威胁检测主类"""
    
    def __init__(self):
        self.anomaly_detector = AnomalyDetector()
        self.intrusion_detector = IntrusionDetector()
        self.threat_events: List[ThreatEvent] = []
        self.network_flows: List[NetworkFlow] = []
        self.threat_intelligence = self._load_threat_intelligence()
        
    def _load_threat_intelligence(self) -> Dict:
        """加载威胁情报"""
        try:
            with open('data/threat_intelligence.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.info("威胁情报文件不存在，使用默认配置")
            return {
                'malicious_ips': [],
                'suspicious_domains': [],
                'known_malware_signatures': [],
                'attack_patterns': {}
            }
    
    def add_network_flow(self, flow: NetworkFlow):
        """添加网络流量数据"""
        self.network_flows.append(flow)
        
        # 保持最近的流量数据
        current_time = time.time()
        self.network_flows = [f for f in self.network_flows 
                             if current_time - f.timestamp <= 3600]  # 保留1小时内的数据
    
    def detect_threats(self) -> List[ThreatEvent]:
        """执行威胁检测"""
        threats = []
        
        try:
            # 异常检测
            if self.anomaly_detector.is_trained and self.network_flows:
                is_anomaly, confidence = self.anomaly_detector.detect_anomaly(
                    self.network_flows[-100:]  # 检测最近100个流量
                )
                
                if is_anomaly:
                    threat = ThreatEvent(
                        event_id=f"anomaly_{int(time.time())}",
                        device_id="anomaly_detector",
                        event_type="anomaly",
                        severity="medium",
                        description="检测到网络流量异常",
                        timestamp=time.time(),
                        source_ip="network",
                        destination_ip="network",
                        protocol="multiple",
                        payload_size=0,
                        features={'confidence': confidence},
                        confidence=confidence,
                        status="detected"
                    )
                    threats.append(threat)
            
            # 入侵检测
            port_scan_threats = self.intrusion_detector.detect_port_scan(self.network_flows)
            ddos_threats = self.intrusion_detector.detect_ddos(self.network_flows)
            
            threats.extend(port_scan_threats)
            threats.extend(ddos_threats)
            
            # 威胁情报匹配
            intel_threats = self._match_threat_intelligence()
            threats.extend(intel_threats)
            
            # 保存威胁事件
            self.threat_events.extend(threats)
            
            if threats:
                logger.warning(f"检测到{len(threats)}个威胁事件")
            
            return threats
            
        except Exception as e:
            logger.error(f"威胁检测失败: {e}")
            return []
    
    def _match_threat_intelligence(self) -> List[ThreatEvent]:
        """匹配威胁情报"""
        threats = []
        
        # 检查恶意IP
        malicious_ips = set(self.threat_intelligence.get('malicious_ips', []))
        for flow in self.network_flows[-100:]:  # 检查最近的流量
            if flow.src_ip in malicious_ips:
                threat = ThreatEvent(
                    event_id=f"malicious_ip_{flow.src_ip}_{int(time.time())}",
                    device_id="threat_intel",
                    event_type="malware",
                    severity="high",
                    description=f"检测到来自已知恶意IP的通信: {flow.src_ip}",
                    timestamp=time.time(),
                    source_ip=flow.src_ip,
                    destination_ip=flow.dst_ip,
                    protocol=flow.protocol,
                    payload_size=flow.byte_count,
                    features={'malicious_ip': flow.src_ip},
                    confidence=0.9,
                    status="detected"
                )
                threats.append(threat)
        
        return threats
    
    def train_anomaly_detector(self, normal_traffic_data: List[List[NetworkFlow]]) -> bool:
        """训练异常检测器"""
        return self.anomaly_detector.train(normal_traffic_data)
    
    def get_threat_summary(self) -> Dict[str, Any]:
        """获取威胁摘要"""
        current_time = time.time()
        recent_threats = [t for t in self.threat_events 
                         if current_time - t.timestamp <= 3600]  # 最近1小时
        
        summary = {
            'total_threats': len(recent_threats),
            'by_type': defaultdict(int),
            'by_severity': defaultdict(int),
            'top_sources': defaultdict(int),
            'latest_threats': recent_threats[-10:] if recent_threats else []
        }
        
        for threat in recent_threats:
            summary['by_type'][threat.event_type] += 1
            summary['by_severity'][threat.severity] += 1
            summary['top_sources'][threat.source_ip] += 1
        
        return dict(summary)
    
    def update_threat_intelligence(self, new_intelligence: Dict):
        """更新威胁情报"""
        self.threat_intelligence.update(new_intelligence)
        
        # 保存到文件
        try:
            with open('data/threat_intelligence.json', 'w', encoding='utf-8') as f:
                json.dump(self.threat_intelligence, f, ensure_ascii=False, indent=2)
            logger.info("威胁情报更新成功")
        except Exception as e:
            logger.error(f"保存威胁情报失败: {e}")
