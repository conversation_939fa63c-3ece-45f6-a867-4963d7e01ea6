"""
物联网安全防护系统主程序
基于课程设计论文实现的安全防护系统
整合了系统运行、测试和管理功能
"""

import time
import asyncio
import signal
import sys
import random
import argparse
import os
from pathlib import Path
from typing import Dict, Any
from datetime import datetime

from config.settings import SecurityConfig
from core.device_manager import DeviceManager
from core.crypto_engine import CryptoEngine
from core.threat_detector import ThreatDetector, NetworkFlow
from utils.logger import get_logger, log_security_event

logger = get_logger(__name__)

def create_directories():
    """创建必要的目录"""
    directories = [
        'data',
        'data/security_logs',
        'certs',
        'keys',
        'ca'
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"创建目录: {directory}")

def test_modules():
    """测试各个模块"""
    logger.info("开始模块测试...")

    try:
        # 测试设备管理器
        logger.info("测试设备管理器...")
        device_manager = DeviceManager()

        test_device = {
            'device_id': 'test_001',
            'device_type': 'sensor',
            'manufacturer': 'TestCorp',
            'model': 'T-100',
            'firmware_version': '1.0.0',
            'mac_address': '00:11:22:33:44:55',
            'ip_address': '***********00'
        }

        success, message = device_manager.register_device(test_device)
        if success:
            logger.info("✓ 设备管理器测试通过")
        else:
            logger.error(f"✗ 设备管理器测试失败: {message}")

        # 测试加密引擎
        logger.info("测试加密引擎...")
        crypto_engine = CryptoEngine()

        # 测试密钥生成
        sm4_key = crypto_engine.generate_key('SM4')
        led_key = crypto_engine.generate_key('LED')

        # 测试加密解密
        test_data = b"Hello, IoT Security!"

        # SM4加密测试
        encrypt_result = crypto_engine.encrypt_data(test_data, 'SM4', sm4_key)
        if encrypt_result['success']:
            decrypt_result = crypto_engine.decrypt_data(
                encrypt_result['ciphertext'], 'SM4', sm4_key, encrypt_result['iv']
            )
            if decrypt_result['success'] and decrypt_result['plaintext'] == test_data:
                logger.info("✓ SM4加密引擎测试通过")
            else:
                logger.error("✗ SM4解密测试失败")
        else:
            logger.error("✗ SM4加密测试失败")

        # LED加密测试
        encrypt_result = crypto_engine.encrypt_data(test_data, 'LED', led_key)
        if encrypt_result['success']:
            decrypt_result = crypto_engine.decrypt_data(
                encrypt_result['ciphertext'], 'LED', led_key
            )
            if decrypt_result['success'] and decrypt_result['plaintext'] == test_data:
                logger.info("✓ LED加密引擎测试通过")
            else:
                logger.error("✗ LED解密测试失败")
        else:
            logger.error("✗ LED加密测试失败")

        # 测试威胁检测器
        logger.info("测试威胁检测器...")
        threat_detector = ThreatDetector()

        # 添加一些测试流量
        test_flows = []
        for i in range(10):
            flow = NetworkFlow(
                src_ip=f"192.168.1.{100+i}",
                dst_ip="***********",
                src_port=12345,
                dst_port=80,
                protocol='TCP',
                packet_count=1,
                byte_count=64,
                duration=0.1,
                flags=['SYN'],
                timestamp=time.time()
            )
            test_flows.append(flow)
            threat_detector.add_network_flow(flow)

        threats = threat_detector.detect_threats()
        logger.info(f"✓ 威胁检测器测试通过，检测到{len(threats)}个威胁")

        logger.info("所有模块测试完成")
        return True

    except Exception as e:
        logger.error(f"模块测试失败: {e}")
        return False

def show_system_info():
    """显示系统信息"""
    print("=" * 60)
    print("物联网安全防护系统信息")
    print("=" * 60)
    print(f"系统名称: {SecurityConfig.SYSTEM_NAME}")
    print(f"版本: {SecurityConfig.VERSION}")
    print(f"调试模式: {'开启' if SecurityConfig.DEBUG else '关闭'}")
    print(f"数据库路径: {SecurityConfig.DATABASE['path']}")
    print(f"日志目录: {SecurityConfig.LOGGING['log_dir']}")
    print(f"最大设备数: {SecurityConfig.DEVICE_CONFIG['max_devices']}")
    print(f"威胁检测间隔: {SecurityConfig.THREAT_DETECTION['detection_interval']}秒")
    print("=" * 60)

    # 显示支持的算法
    crypto_engine = CryptoEngine()
    algo_info = crypto_engine.get_algorithm_info()

    print("支持的加密算法:")
    print("对称加密:", ", ".join(algo_info['symmetric'].keys()))
    print("非对称加密:", ", ".join(algo_info['asymmetric'].keys()))
    print("哈希算法:", ", ".join(algo_info['hash'].keys()))
    print("=" * 60)

class IoTSecuritySystem:
    """物联网安全防护系统主类"""

    def __init__(self):
        self.device_manager = DeviceManager()
        self.crypto_engine = CryptoEngine()
        self.threat_detector = ThreatDetector()
        self.running = False
        self.stats = {
            'start_time': None,
            'devices_registered': 0,
            'threats_detected': 0,
            'data_encrypted': 0,
            'authentications': 0
        }

    async def start(self):
        """启动安全防护系统"""
        try:
            logger.info("=" * 60)
            logger.info("物联网安全防护系统启动中...")
            logger.info(f"系统版本: {SecurityConfig.SYSTEM_NAME} v{SecurityConfig.VERSION}")
            logger.info("=" * 60)

            self.running = True
            self.stats['start_time'] = datetime.now()

            # 初始化各个模块
            await self._initialize_modules()

            # 启动监控任务
            tasks = [
                asyncio.create_task(self._device_monitor()),
                asyncio.create_task(self._threat_monitor()),
                asyncio.create_task(self._system_monitor()),
                asyncio.create_task(self._demo_simulation())
            ]

            logger.info("系统启动完成，开始监控...")
            log_security_event("SYSTEM_START", "物联网安全防护系统启动成功")

            # 等待所有任务完成
            await asyncio.gather(*tasks)

        except Exception as e:
            logger.error(f"系统启动失败: {e}")
            await self.stop()

    async def _initialize_modules(self):
        """初始化各个模块"""
        logger.info("初始化设备管理模块...")
        # 设备管理器已在构造函数中初始化

        logger.info("初始化加密引擎...")
        # 测试加密功能
        test_key = self.crypto_engine.generate_key('SM4')
        logger.info(f"生成测试密钥成功，长度: {len(test_key)}字节")

        logger.info("初始化威胁检测模块...")
        # 加载威胁情报
        self.threat_detector._load_threat_intelligence()

        logger.info("所有模块初始化完成")

    async def _device_monitor(self):
        """设备监控任务"""
        while self.running:
            try:
                # 检查离线设备
                offline_devices = self.device_manager.check_offline_devices()
                if offline_devices:
                    log_security_event("DEVICE_OFFLINE",
                                     f"发现{len(offline_devices)}个离线设备",
                                     devices=offline_devices)

                # 定期保存设备注册表
                self.device_manager.save_device_registry()

                await asyncio.sleep(SecurityConfig.DEVICE_CONFIG['heartbeat_interval'])

            except Exception as e:
                logger.error(f"设备监控任务异常: {e}")
                await asyncio.sleep(10)

    async def _threat_monitor(self):
        """威胁监控任务"""
        while self.running:
            try:
                # 执行威胁检测
                threats = self.threat_detector.detect_threats()

                if threats:
                    self.stats['threats_detected'] += len(threats)
                    for threat in threats:
                        log_security_event(
                            "THREAT_DETECTED",
                            f"{threat.event_type}威胁: {threat.description}",
                            severity=threat.severity,
                            source_ip=threat.source_ip,
                            confidence=threat.confidence
                        )

                await asyncio.sleep(SecurityConfig.THREAT_DETECTION['detection_interval'])

            except Exception as e:
                logger.error(f"威胁监控任务异常: {e}")
                await asyncio.sleep(10)

    async def _system_monitor(self):
        """系统监控任务"""
        while self.running:
            try:
                # 输出系统状态
                uptime = datetime.now() - self.stats['start_time']
                device_count = len(self.device_manager.get_all_devices())
                threat_summary = self.threat_detector.get_threat_summary()

                logger.info(f"系统状态 - 运行时间: {uptime}, "
                          f"设备数量: {device_count}, "
                          f"威胁检测: {threat_summary['total_threats']}")

                await asyncio.sleep(60)  # 每分钟输出一次状态

            except Exception as e:
                logger.error(f"系统监控任务异常: {e}")
                await asyncio.sleep(30)

    async def _demo_simulation(self):
        """演示模拟任务"""
        logger.info("开始演示模拟...")

        # 模拟设备注册
        await self._simulate_device_registration()

        # 模拟网络流量
        await self._simulate_network_traffic()

        # 模拟威胁事件
        await self._simulate_threat_events()

    async def _simulate_device_registration(self):
        """模拟设备注册"""
        demo_devices = [
            {
                'device_id': 'sensor_001',
                'device_type': 'temperature_sensor',
                'manufacturer': 'Huawei',
                'model': 'HW-T100',
                'firmware_version': '2.1.0',
                'mac_address': '00:1A:2B:3C:4D:5E',
                'ip_address': '***********00'
            },
            {
                'device_id': 'camera_001',
                'device_type': 'security_camera',
                'manufacturer': 'Xiaomi',
                'model': 'MI-CAM-V1',
                'firmware_version': '1.8.5',
                'mac_address': '00:1A:2B:3C:4D:5F',
                'ip_address': '***********01'
            },
            {
                'device_id': 'gateway_001',
                'device_type': 'iot_gateway',
                'manufacturer': 'Samsung',
                'model': 'SG-GW-200',
                'firmware_version': '3.0.1',
                'mac_address': '00:1A:2B:3C:4D:60',
                'ip_address': '***********'
            }
        ]

        for device_info in demo_devices:
            success, message = self.device_manager.register_device(device_info)
            if success:
                self.stats['devices_registered'] += 1
                logger.info(f"演示设备注册成功: {device_info['device_id']}")
            else:
                logger.warning(f"演示设备注册失败: {device_info['device_id']}, {message}")

            await asyncio.sleep(2)

    async def _simulate_network_traffic(self):
        """模拟网络流量"""

        for i in range(50):
            flow = NetworkFlow(
                src_ip=f"192.168.1.{random.randint(100, 200)}",
                dst_ip=f"10.0.0.{random.randint(1, 100)}",
                src_port=random.randint(1024, 65535),
                dst_port=random.choice([80, 443, 22, 21, 25]),
                protocol=random.choice(['TCP', 'UDP', 'ICMP']),
                packet_count=random.randint(1, 100),
                byte_count=random.randint(64, 1500),
                duration=random.uniform(0.1, 10.0),
                flags=['SYN', 'ACK'] if random.random() > 0.5 else ['FIN'],
                timestamp=time.time()
            )

            self.threat_detector.add_network_flow(flow)
            await asyncio.sleep(0.1)

    async def _simulate_threat_events(self):
        """模拟威胁事件"""
        await asyncio.sleep(30)  # 等待一些正常流量

        # 模拟端口扫描
        logger.info("模拟端口扫描攻击...")
        for port in range(20, 100):
            flow = NetworkFlow(
                src_ip="*************",  # 攻击者IP
                dst_ip="***********00",
                src_port=12345,
                dst_port=port,
                protocol='TCP',
                packet_count=1,
                byte_count=64,
                duration=0.1,
                flags=['SYN'],
                timestamp=time.time()
            )
            self.threat_detector.add_network_flow(flow)
            await asyncio.sleep(0.05)

        # 模拟DDoS攻击
        logger.info("模拟DDoS攻击...")
        for i in range(1000):
            flow = NetworkFlow(
                src_ip=f"10.0.{i//256}.{i%256}",
                dst_ip="***********",
                src_port=random.randint(1024, 65535),
                dst_port=80,
                protocol='TCP',
                packet_count=1,
                byte_count=random.randint(64, 1500),
                duration=0.01,
                flags=['SYN'],
                timestamp=time.time()
            )
            self.threat_detector.add_network_flow(flow)
            if i % 100 == 0:
                await asyncio.sleep(0.1)

    async def stop(self):
        """停止系统"""
        logger.info("正在停止物联网安全防护系统...")
        self.running = False

        # 保存最终状态
        self.device_manager.save_device_registry()

        # 输出统计信息
        uptime = datetime.now() - self.stats['start_time'] if self.stats['start_time'] else None
        logger.info("=" * 60)
        logger.info("系统运行统计:")
        logger.info(f"运行时间: {uptime}")
        logger.info(f"注册设备数: {self.stats['devices_registered']}")
        logger.info(f"检测威胁数: {self.stats['threats_detected']}")
        logger.info(f"认证次数: {self.stats['authentications']}")
        logger.info("=" * 60)

        log_security_event("SYSTEM_STOP", "物联网安全防护系统已停止")
        logger.info("系统已安全停止")

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"接收到信号 {signum}，准备停止系统...")
    sys.exit(0)

async def run_system():
    """运行系统"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 创建并启动系统
    system = IoTSecuritySystem()

    try:
        await system.start()
    except KeyboardInterrupt:
        logger.info("接收到中断信号...")
    except Exception as e:
        logger.error(f"系统运行异常: {e}")
    finally:
        await system.stop()

def main():
    """主函数 - 命令行界面"""
    parser = argparse.ArgumentParser(description='物联网安全防护系统')
    parser.add_argument('--action', choices=['run', 'test', 'info', 'setup'],
                       default='run', help='执行的操作')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')

    args = parser.parse_args()

    if args.debug:
        SecurityConfig.DEBUG = True
        SecurityConfig.LOGGING['level'] = 'DEBUG'

    if args.action == 'setup':
        logger.info("初始化系统环境...")
        create_directories()
        logger.info("系统环境初始化完成")

    elif args.action == 'test':
        logger.info("开始系统测试...")
        create_directories()
        if test_modules():
            logger.info("系统测试通过")
            sys.exit(0)
        else:
            logger.error("系统测试失败")
            sys.exit(1)

    elif args.action == 'info':
        show_system_info()

    elif args.action == 'run':
        logger.info("启动物联网安全防护系统...")
        create_directories()

        # 可选：运行测试
        if not args.debug:
            logger.info("运行快速测试...")
            if not test_modules():
                logger.warning("模块测试失败，但继续运行系统")

        # 运行系统
        try:
            asyncio.run(run_system())
        except KeyboardInterrupt:
            logger.info("程序被用户中断")
        except Exception as e:
            logger.error(f"程序异常退出: {e}")
            sys.exit(1)

if __name__ == "__main__":
    main()
